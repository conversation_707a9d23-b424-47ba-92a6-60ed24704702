import requests
import time
import logging
import os
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class TPAApiService:
    """Service for TPA (Third Party Administrator) API integration"""

    BASE_URL = "https://uat.thirdpartyadmin.co.th:4443/TPA.TMS.Web.API_PREPROV2"

    # TPA API credentials
    USERNAME = "BVTPA"
    PASSWORD = "*d!n^+Cb@1"

    def __init__(self):
        self.session = requests.Session()
        # Set default timeout for all requests
        self.default_timeout = 30

        # Configure SSL verification based on environment variables
        # For development/testing with expired certificates
        tpa_ssl_verify_env = os.getenv('TPA_SSL_VERIFY', 'true')
        self.verify_ssl = tpa_ssl_verify_env.lower() == 'true'

        # Apply SSL verification setting to the session
        self.session.verify = self.verify_ssl

        # Debug logging
        logger.info(f"TPA SSL Configuration - Environment variable TPA_SSL_VERIFY: '{tpa_ssl_verify_env}'")
        logger.info(f"TPA SSL Configuration - SSL verification enabled: {self.verify_ssl}")
        logger.info(f"TPA SSL Configuration - Session verify setting: {self.session.verify}")

        if not self.verify_ssl:
            logger.warning("TPA SSL verification is disabled. This should only be used in development/testing.")
            # Disable SSL warnings when verification is disabled
            try:
                import urllib3
                urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
                logger.info("SSL warnings disabled successfully")
            except ImportError:
                logger.warning("urllib3 not available, SSL warnings cannot be disabled")
    
    def _make_request_with_retry(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request with retry logic"""
        max_retries = 3
        retry_delay = 3  # seconds

        # Add default timeout if not specified
        if 'timeout' not in kwargs:
            kwargs['timeout'] = self.default_timeout

        # Debug logging
        logger.info(f"TPA API Request - Method: {method}, URL: {url}")
        logger.info(f"TPA API Request - Session verify: {self.session.verify}")
        logger.info(f"TPA API Request - Kwargs verify: {kwargs.get('verify', 'not set')}")

        last_exception = None
        for attempt in range(max_retries):
            try:
                response = self.session.request(method, url, **kwargs)
                response.raise_for_status()
                logger.info(f"TPA API Request - Success on attempt {attempt + 1}")
                return response
            except requests.exceptions.RequestException as e:
                last_exception = e
                logger.warning(f"TPA API request failed (attempt {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay * (2 ** attempt))  # Exponential backoff

        # If we get here, all retries failed
        logger.error(f"TPA API Request - All {max_retries} attempts failed. Final error: {str(last_exception)}")
        raise last_exception or requests.exceptions.RequestException("All retry attempts failed")
    
    def get_bearer_token(self, social_id: str, channel_id: str, channel: str) -> str:
        """Get bearer token from TPA API"""
        url = f"{self.BASE_URL}/api/GetToken"
        
        payload = {
            "USERNAME": self.USERNAME,
            "PASSWORD": self.PASSWORD,
            "SOCIAL_ID": social_id,
            "CHANNEL_ID": channel_id,
            "CHANNEL": channel
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        try:
            response = self._make_request_with_retry("POST", url, data=payload, headers=headers)
            token_data = response.text
            return token_data
        except Exception as e:
            logger.error(f"Failed to get TPA bearer token: {str(e)}")
            raise Exception(f"TPA authentication failed: {str(e)}")
    
    def verify_citizen_id(self, token: str, citizen_id: str) -> Dict[str, Any]:
        """Verify citizen ID with TPA API"""
        url = f"{self.BASE_URL}/api/SearchCitizenID"
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "CitizenID": citizen_id
        }
        
        try:
            response = self._make_request_with_retry("POST", url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to verify citizen ID {citizen_id}: {str(e)}")
            raise Exception(f"Citizen ID verification failed: {str(e)}")
    
    def check_registration(self, token: str, citizen_id: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """Check registration status with TPA API"""
        url = f"{self.BASE_URL}/api/CheckRegister"
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id
        }
        
        try:
            response = self._make_request_with_retry("POST", url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to check registration for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Registration check failed: {str(e)}")
    
    def get_policy_list(self, token: str, citizen_id: str, social_id: str, channel_id: str) -> Dict[str, Any]:
        """Get policy list from TPA API"""
        url = f"{self.BASE_URL}/api/PolicyListSocial"
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id
        }
        
        try:
            response = self._make_request_with_retry("POST", url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get policy list for citizen {citizen_id}: {str(e)}")
            raise Exception(f"Policy list retrieval failed: {str(e)}")
    
    def get_policy_details(self, token: str, citizen_id: str, social_id: str, channel_id: str, member_code: str) -> Dict[str, Any]:
        """Get policy details from TPA API"""
        url = f"{self.BASE_URL}/api/PolicyDetailSocial"
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "CitizenID": citizen_id,
            "SocialID": social_id,
            "ChannelID": channel_id,
            "MemberCode": member_code
        }
        
        try:
            response = self._make_request_with_retry("POST", url, json=payload, headers=headers)
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get policy details for member {member_code}: {str(e)}")
            raise Exception(f"Policy details retrieval failed: {str(e)}")

    # Dynamic request methods for JSON-driven workflows
    def make_dynamic_request(self, endpoint: str, method: str, payload: Dict[str, Any],
                           headers: Optional[Dict[str, str]] = None) -> Any:
        """Make a dynamic API request with configurable payload and headers"""
        url = f"{self.BASE_URL}{endpoint}"

        # Default headers
        request_headers = {
            "Content-Type": "application/json"
        }

        # Add custom headers if provided
        if headers:
            request_headers.update(headers)

        try:
            # Determine request method and payload format
            if method.upper() == "POST":
                if request_headers.get("Content-Type") == "application/x-www-form-urlencoded":
                    response = self._make_request_with_retry(method, url, data=payload, headers=request_headers)
                else:
                    response = self._make_request_with_retry(method, url, json=payload, headers=request_headers)
            else:
                response = self._make_request_with_retry(method, url, params=payload, headers=request_headers)

            # Handle different response types
            content_type = response.headers.get('content-type', '').lower()
            if 'application/json' in content_type:
                return response.json()
            else:
                # For token endpoint which returns plain text
                return response.text.strip().replace('"', '')

        except Exception as e:
            logger.error(f"Dynamic API request failed for {endpoint}: {str(e)}")
            raise Exception(f"API request to {endpoint} failed: {str(e)}")


